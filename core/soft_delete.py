from django.db import models
from django.utils import timezone

class SoftDeleteQuerySet(models.QuerySet):
    
    # 批量软删除
    def delete(self):
        return super().update(deleted_at=timezone.now())

    # 真正的物理删除
    def hard_delete(self):
        return super().delete()

    # 获取未删除的记录
    def alive(self):
        return self.filter(deleted_at__isnull=True)

    # 获取已删除的记录
    def dead(self):
        return self.filter(deleted_at__isnull=False)


# 软删除管理器
class SoftDeleteManager(models.Manager):
    # 获取未删除的记录
    def get_queryset(self):
        return SoftDeleteQuerySet(self.model, using=self._db).alive()

    # 获取所有记录（包括已删除的）
    def all_with_deleted(self):
        return SoftDeleteQuerySet(self.model, using=self._db)
    
    # 获取已删除的记录
    def deleted_only(self):
        return SoftDeleteQuerySet(self.model, using=self._db).dead()


# 包含已删除记录的管理器
class AllObjectsManager(models.Manager):
    
    # 获取所有记录（包括已删除的）
    def get_queryset(self):
        return SoftDeleteQuerySet(self.model, using=self._db)