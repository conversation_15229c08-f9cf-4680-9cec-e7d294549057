from ulid import ULID
import secrets
import time
from typing import Optional

# 生成资源id
def generate_resource_ulid(prefix: Optional[str] = None) -> str:

    try:
        
        ulid = str(ULID())
        
        if len(ulid) == 26:
            extra_char = secrets.choice('0123456789ABCDEFGHJKMNPQRSTVWXYZ')
            ulid = ulid + extra_char
            
        elif len(ulid) > 27:
            ulid = ulid[:27]

        if prefix:
            clean_prefix = prefix.rstrip('_')
            return f"{clean_prefix}_{ulid}"

        return ulid

    except Exception as e:
        timestamp = int(time.time() * 1000)
        random_part = secrets.token_urlsafe(11)[:14]
        backup_id = f"{timestamp:013d}{random_part}".upper()[:27]
        
        if prefix:
            clean_prefix = prefix.rstrip('_')
            return f"{clean_prefix}_{backup_id}"
        
        return backup_id



# 生成用户id
def generate_user_id(prefix: Optional[str] = None) -> str:
    try:
        ulid = str(ULID())
        
        ulid = ulid[:17]

        if prefix:
            clean_prefix = prefix.rstrip('_')
            return f"{clean_prefix}_{ulid}"

        return ulid

    except Exception as e:
        timestamp = int(time.time() * 1000)
        random_part = secrets.token_urlsafe(8)[:8]  
        backup_id = f"{timestamp:013d}{random_part}".upper()[:17] 
        
        if prefix:
            clean_prefix = prefix.rstrip('_')
            return f"{clean_prefix}_{backup_id}"
        
        return backup_id


# 生成客户id
def generate_customer_id():
    return generate_user_id('CUS_')

# 生成联系人 id
def generate_contact_id():
    return generate_user_id()

# 生成员工id
def generate_staff_id():
    return generate_user_id('STA_')