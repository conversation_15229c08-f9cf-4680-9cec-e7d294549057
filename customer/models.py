from django.db import models
from django.utils import timezone

from core.generate_resource_id import generate_contact_id, generate_customer_id
from core.model import BaseModel
from core.soft_delete import AllObjectsManager, SoftDeleteManager
from customer.enums import CustomerImportance, CustomerType


# 客户模型
class Customer(BaseModel):

    # 客户类型
    customer_type = models.CharField(max_length=50, choices=CustomerType.choices,verbose_name='客户类型')
    # 客户名称
    name = models.CharField(max_length=50,verbose_name='客户名称')
    # 客户电话
    phone = models.CharField(max_length=50,verbose_name='客户电话')
    # 客户地址
    address = models.CharField(max_length=255,verbose_name='客户地址')
    # 客户邮箱
    email = models.EmailField(max_length=255,verbose_name='客户邮箱')
    # 客户重要程度
    importance = models.CharField(max_length=50, choices=CustomerImportance.choices,verbose_name='客户重要程度')
    # 客户备注
    remark = models.TextField(verbose_name='客户备注')
    # 客户id
    uid = models.CharField(max_length=50,verbose_name='客户id',unique=True,default=generate_customer_id,blank=True)
    # 软删除时间戳
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')

    # 默认管理器（只返回未删除的记录）
    objects = SoftDeleteManager()
    # 包含所有记录的管理器
    all_objects = AllObjectsManager()

    class Meta:
        verbose_name = '客户信息'
        verbose_name_plural = '客户信息'
        constraints = [
            models.UniqueConstraint(
                fields=['uid'],
                condition=models.Q(deleted_at__isnull=True),
                name='unique_customer_uid_when_not_deleted'
            ),
            models.UniqueConstraint(
                fields=['phone'],
                condition=models.Q(deleted_at__isnull=True),
                name='unique_customer_phone_when_not_deleted'
            ),
            models.UniqueConstraint(
                fields=['email'],
                condition=models.Q(deleted_at__isnull=True),
                name='unique_customer_email_when_not_deleted'
            ),
        ]

    def __str__(self):
        return f"{self.name}{'(已删除)' if self.deleted_at else ''}"

    # 软删除方法
    def delete(self, using=None, keep_parents=False):
        if self.deleted_at:
            return
        self.deleted_at = timezone.now()
        self.save(using=using)

    # 判断是否已被软删除
    @property
    def is_deleted(self):
        return self.deleted_at is not None

    # 检查电话号码是否已存在（只检查未删除的记录）
    @classmethod
    def check_phone_exist(cls, phone, exclude_id=None):
        queryset = cls.objects.filter(phone=phone)
        if exclude_id:
            queryset = queryset.exclude(id=exclude_id)
        return queryset.exists()

    # 检查邮箱是否已存在（只检查未删除的记录）
    @classmethod
    def check_email_exist(cls, email, exclude_id=None):
        queryset = cls.objects.filter(email=email)
        if exclude_id:
            queryset = queryset.exclude(id=exclude_id)
        return queryset.exists()

    # 根据uid获取客户
    @classmethod
    def get_customer_by_uid(cls, uid):
        try:
            return cls.objects.get(uid=uid)
        except cls.DoesNotExist:
            return None


# 客户联系人
class CustomerContact(BaseModel):
    # 客户
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='contacts')
    # 联系人姓名
    contact_name = models.CharField(max_length=50, verbose_name='联系人姓名')
    # 联系人电话
    contact_phone = models.CharField(max_length=50, verbose_name='联系人电话')
    # 联系人id
    contact_id = models.CharField(max_length=50,verbose_name='联系人id',unique=True,default=generate_contact_id,blank=True)
    # 软删除时间戳
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')

    # 默认管理器（只返回未删除的记录）
    objects = SoftDeleteManager()
    # 包含所有记录的管理器
    all_objects = AllObjectsManager()

    class Meta:
        verbose_name = '客户联系人'
        verbose_name_plural = '客户联系人'
        constraints = [
            models.UniqueConstraint(
                fields=['contact_id'],
                condition=models.Q(deleted_at__isnull=True),
                name='unique_contact_id_when_not_deleted'
            ),
        ]

    def __str__(self):
        return f"{self.contact_name}{'(已删除)' if self.deleted_at else ''}"

    # 软删除方法
    def delete(self, using=None, keep_parents=False):
        if self.deleted_at:
            return
        self.deleted_at = timezone.now()
        self.save(using=using)

    # 判断是否已被软删除
    @property
    def is_deleted(self):
        return self.deleted_at is not None

    # 根据contact_id获取联系人
    @classmethod
    def get_contact_by_id(cls, contact_id):
        try:
            return cls.objects.get(contact_id=contact_id)
        except cls.DoesNotExist:
            return None