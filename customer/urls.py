from django.urls import path

from customer.views import (
    CustomerCreateView, CustomerDetailView, CustomerDeleteView, CustomerListView, CustomerUpdateView,
    CustomerContactCreateView, CustomerContactDeleteView, CustomerContactUpdateView
)

urlpatterns = [
    # 客户相关接口
    # 客户列表
    path('list/', CustomerListView.as_view(), name='customer-list'),
    # 客户详情
    path('detail/<str:uid>/', CustomerDetailView.as_view(), name='customer-detail'),
    # 客户创建
    path('create/', CustomerCreateView.as_view(), name='customer-create'),
    # 客户更新
    path('update/<str:uid>/', CustomerUpdateView.as_view(), name='customer-update'),
    # 客户删除
    path('delete/<str:uid>/', CustomerDeleteView.as_view(), name='customer-delete'),

    # 客户联系人相关接口
    # 客户联系人创建
    path('contact/create/', CustomerContactCreateView.as_view(), name='customer-contact-create'),
    # 客户联系人更新
    path('contact/update/<str:contact_id>/', CustomerContactUpdateView.as_view(), name='customer-contact-update'),
    # 客户联系人删除
    path('contact/delete/<str:contact_id>/', CustomerContactDeleteView.as_view(), name='customer-contact-delete'),
]
