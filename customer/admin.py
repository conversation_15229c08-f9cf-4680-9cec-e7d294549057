from django.contrib import admin
from django.utils.html import format_html
from .models import Customer, CustomerContact


# 客户联系人内联管理
class CustomerContactInline(admin.TabularInline):
    model = CustomerContact
    extra = 1
    fields = ('contact_id', 'contact_name', 'contact_phone')
    verbose_name = '联系人'
    verbose_name_plural = '联系人列表'


# 客户管理
@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    # 列表页显示的字段
    list_display = (
        'uid',
        'name',
        'customer_type_display',
        'phone',
        'email',
        'importance_display',
        'contact_count',
        'created_at'
    )

    # 列表页过滤器
    list_filter = (
        'customer_type',
        'importance',
        'created_at',
        'updated_at'
    )

    # 搜索字段
    search_fields = (
        'name',
        'phone',
        'email',
        'uid',
        'address'
    )

    # 详情页字段分组
    fieldsets = (
        ('基本信息', {
            'fields': ('uid', 'name', 'customer_type')
        }),
        ('联系方式', {
            'fields': ('phone', 'email', 'address')
        }),
        ('其他信息', {
            'fields': ('importance', 'remark'),
        }),
    )

    # 只读字段
    readonly_fields = ('uid', 'created_at', 'updated_at')

    # 每页显示数量
    list_per_page = 20

    # 排序
    ordering = ('-created_at',)

    # 内联管理联系人
    inlines = [CustomerContactInline]

    # 自定义方法：显示客户类型
    def customer_type_display(self, obj):
        type_colors = {
            'OWNER': '#28a745',  # 绿色
            'GENERAL_CONTRACTOR': '#007bff',  # 蓝色
            'INTERMEDIARY': '#ffc107',  # 黄色
        }
        color = type_colors.get(obj.customer_type, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_customer_type_display()
        )
    customer_type_display.short_description = '客户类型'
    customer_type_display.admin_order_field = 'customer_type'

    # 自定义方法：显示重要程度
    def importance_display(self, obj):
        importance_colors = {
            'HIGH': '#dc3545',  # 红色
            'MEDIUM': '#fd7e14',  # 橙色
            'LOW': '#6c757d',  # 灰色
        }
        color = importance_colors.get(obj.importance, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color,
            obj.get_importance_display()
        )
    importance_display.short_description = '重要程度'
    importance_display.admin_order_field = 'importance'

    # 自定义方法：显示联系人数量
    def contact_count(self, obj):
        count = obj.contacts.count()
        if count > 0:
            return format_html(
                '<span style="color: #007bff;">{} 个联系人</span>',
                count
            )
        return format_html('<span style="color: #6c757d;">无联系人</span>')
    contact_count.short_description = '联系人'


# 客户联系人管理
@admin.register(CustomerContact)
class CustomerContactAdmin(admin.ModelAdmin):
    # 列表页显示的字段
    list_display = (
        'contact_id',
        'contact_name',
        'contact_phone',
        'customer_link',
        'customer_type_display',
        'created_at'
    )

    # 列表页过滤器
    list_filter = (
        'customer__customer_type',
        'customer__importance',
        'created_at'
    )

    # 搜索字段
    search_fields = (
        'contact_name',
        'contact_phone',
        'customer__name',
        'customer__uid'
    )

    # 详情页字段
    fields = (
        'customer',
        'contact_name',
        'contact_phone',
        'created_at',
        'updated_at'
    )

    # 只读字段
    readonly_fields = ('created_at', 'updated_at')

    # 每页显示数量
    list_per_page = 25

    # 排序
    ordering = ('-created_at',)

    # 自定义方法：显示客户链接
    def customer_link(self, obj):
        return format_html(
            '<a href="/admin/customer/customer/{}/change/" style="color: #007bff;">{}</a>',
            obj.customer.id,
            obj.customer.name
        )
    customer_link.short_description = '所属客户'
    customer_link.admin_order_field = 'customer__name'

    # 自定义方法：显示客户类型
    def customer_type_display(self, obj):
        return obj.customer.get_customer_type_display()
    customer_type_display.short_description = '客户类型'
    customer_type_display.admin_order_field = 'customer__customer_type'
