from rest_framework.views import APIView

from core.resp import make_error_response, make_success_response
from core.view import PaginationListBaseView
from customer.enums import CustomerImportance, CustomerType
from customer.models import Customer, CustomerContact
from customer.serializers import (
    CustomerCreateSerializer, CustomerDetailSerializer, CustomerListSerializer, CustomerUpdateSerializer,
    CustomerContactCreateSerializer, CustomerContactDetailSerializer, CustomerContactUpdateSerializer
)


# 客户列表
class CustomerListView(PaginationListBaseView):
    authentication_classes = []
    permission_classes = []
    staff_required_permission = None

    serializer_class = CustomerListSerializer
    response_msg = "Success"
    error_response_msg = "Failed"
    search_fields = ['name', 'phone', 'email', 'address', 'customer_type', 'importance']
    additional_data = None
    audit_log_message = ""

    def get_queryset(self):
        base_queryset = Customer.objects.filter().order_by('-created_at')

        customer_type = self.request.query_params.get('customer_type', None)
        importance = self.request.query_params.get('importance', None)

        if customer_type and customer_type in CustomerType.values:
            base_queryset = base_queryset.filter(customer_type=customer_type)

        if importance and importance in CustomerImportance.values:
            base_queryset = base_queryset.filter(importance=importance)

        return base_queryset


# 客户详情
class CustomerDetailView(APIView):
    authentication_classes = []
    permission_classes = []

    def get(self, request, uid):
        customer = Customer.get_customer_by_uid(uid)
        if not customer:
            return make_error_response(msg="客户不存在")
        return make_success_response(CustomerDetailSerializer(customer).data)


# 客户创建
class CustomerCreateView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        data = request.data.copy()

        serializer = CustomerCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_success_response(CustomerDetailSerializer(serializer.instance).data)
        return make_error_response(serializer.errors)


# 客户更新
class CustomerUpdateView(APIView):
    authentication_classes = []
    permission_classes = []

    def put(self, request, uid):
        customer = Customer.get_customer_by_uid(uid)
        if not customer:
            return make_error_response(msg="客户不存在")

        data = request.data.copy()

        serializer = CustomerUpdateSerializer(customer, data=data)
        if serializer.is_valid():
            serializer.save()
            return make_success_response(CustomerDetailSerializer(serializer.instance).data)
        return make_error_response(serializer.errors)


# 客户删除
class CustomerDeleteView(APIView):
    authentication_classes = []
    permission_classes = []

    def delete(self, request, uid):
        customer = Customer.get_customer_by_uid(uid)

        if not customer:
            return make_error_response(msg="客户不存在")

        customer.delete()

        return make_success_response(msg="客户删除成功")


# 客户联系人创建
class CustomerContactCreateView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        data = request.data.copy()

        serializer = CustomerContactCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_success_response(CustomerContactDetailSerializer(serializer.instance).data)
        return make_error_response(serializer.errors)


# 客户联系人更新
class CustomerContactUpdateView(APIView):
    authentication_classes = []
    permission_classes = []

    def put(self, request, contact_id):
        contact = CustomerContact.get_contact_by_id(contact_id)
        if not contact:
            return make_error_response(msg="联系人不存在")

        data = request.data.copy()

        serializer = CustomerContactUpdateSerializer(contact, data=data)
        if serializer.is_valid():
            serializer.save()
            return make_success_response(CustomerContactDetailSerializer(serializer.instance).data)
        return make_error_response(serializer.errors)


# 客户联系人删除
class CustomerContactDeleteView(APIView):
    authentication_classes = []
    permission_classes = []

    def delete(self, request, contact_id):
        contact = CustomerContact.get_contact_by_id(contact_id)

        if not contact:
            return make_error_response(msg="联系人不存在")

        contact.delete()

        return make_success_response(msg="联系人删除成功")
