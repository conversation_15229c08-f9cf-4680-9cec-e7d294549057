from rest_framework import serializers
from django.db import transaction

from core.parse_time import ShanghaiFriendlyDateTimeField
from customer.enums import CustomerImportance, CustomerType
from customer.models import Customer, CustomerContact


# 客户联系人序列化器（用于嵌套在客户详情中）
class CustomerContactNestedSerializer(serializers.ModelSerializer):
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()

    class Meta:
        model = CustomerContact
        fields = ['contact_id', 'contact_name', 'contact_phone', 'created_at', 'updated_at']


# 客户列表序列化器
class CustomerListSerializer(serializers.ModelSerializer):
    # 联系人数量
    contact_count = serializers.SerializerMethodField()

    class Meta:
        model = Customer
        fields = ['uid', 'name', 'customer_type', 'phone', 'email', 'importance', 'address', 'contact_count', 'remark']

    def get_contact_count(self, obj):
        return obj.contacts.count()


# 客户详情序列化器
class CustomerDetailSerializer(serializers.ModelSerializer):
    # 联系人列表
    contacts = CustomerContactNestedSerializer(many=True, read_only=True)
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()

    class Meta:
        model = Customer
        fields = ['uid', 'name', 'customer_type', 'phone', 'email', 'importance', 'address', 'remark', 'contacts', 'created_at', 'updated_at']


# 客户创建序列化器
class CustomerCreateSerializer(serializers.ModelSerializer):
    # 客户类型
    customer_type = serializers.CharField()
    # 客户重要程度
    importance = serializers.CharField()
    # 客户电话
    phone = serializers.CharField()
    # 客户邮箱
    email = serializers.EmailField()
    # 联系人列表（可选）
    contacts = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True,
        help_text="联系人列表，每个联系人包含 contact_name 和 contact_phone 字段"
    )

    class Meta:
        model = Customer
        fields = ['name', 'customer_type', 'phone', 'email', 'importance', 'address', 'remark', 'contacts']

    # 验证客户类型
    def validate_customer_type(self, value):
        if value not in CustomerType.values:
            raise serializers.ValidationError("客户类型不正确")
        return value

    # 验证客户重要程度
    def validate_importance(self, value):
        if value not in CustomerImportance.values:
            raise serializers.ValidationError("客户重要程度不正确")
        return value

    # 验证电话是否重复
    def validate_phone(self, value):
        if Customer.check_phone_exist(value, self.instance.id if self.instance else None):
            raise serializers.ValidationError("电话号码已存在")
        return value

    # 验证邮箱是否重复
    def validate_email(self, value):
        if Customer.check_email_exist(value, self.instance.id if self.instance else None):
            raise serializers.ValidationError("邮箱已存在")
        return value

    # 验证联系人数据
    def validate_contacts(self, value):
        if not isinstance(value, list):
            raise serializers.ValidationError("联系人数据格式不正确")

        for contact in value:
            if not isinstance(contact, dict):
                raise serializers.ValidationError("联系人数据格式不正确")

            if 'contact_name' not in contact or 'contact_phone' not in contact:
                raise serializers.ValidationError("联系人必须包含姓名和电话")

            if not contact['contact_name'] or not contact['contact_phone']:
                raise serializers.ValidationError("联系人姓名和电话不能为空")

        return value

    def create(self, validated_data):
        # 提取联系人数据
        contacts_data = validated_data.pop('contacts', [])

        # 使用事务确保数据一致性
        with transaction.atomic():
            # 创建客户
            customer = Customer.objects.create(**validated_data)

            # 创建联系人
            for contact_data in contacts_data:
                CustomerContact.objects.create(
                    customer=customer,
                    contact_name=contact_data['contact_name'],
                    contact_phone=contact_data['contact_phone']
                )

        return customer


# 客户更新序列化器
class CustomerUpdateSerializer(serializers.ModelSerializer):
    # 客户类型
    customer_type = serializers.CharField()
    # 客户重要程度
    importance = serializers.CharField()
    # 客户电话
    phone = serializers.CharField()
    # 客户邮箱
    email = serializers.EmailField()

    class Meta:
        model = Customer
        fields = ['name', 'customer_type', 'phone', 'email', 'importance', 'address', 'remark']

    # 验证客户类型
    def validate_customer_type(self, value):
        if value not in CustomerType.values:
            raise serializers.ValidationError("客户类型不正确")
        return value

    # 验证客户重要程度
    def validate_importance(self, value):
        if value not in CustomerImportance.values:
            raise serializers.ValidationError("客户重要程度不正确")
        return value

    # 验证电话是否重复
    def validate_phone(self, value):
        if Customer.check_phone_exist(value, self.instance.id if self.instance else None):
            raise serializers.ValidationError("电话号码已存在")
        return value

    # 验证邮箱是否重复
    def validate_email(self, value):
        if Customer.check_email_exist(value, self.instance.id if self.instance else None):
            raise serializers.ValidationError("邮箱已存在")
        return value


# 客户联系人创建序列化器
class CustomerContactCreateSerializer(serializers.ModelSerializer):
    # 客户UID
    customer_uid = serializers.CharField(write_only=True)

    class Meta:
        model = CustomerContact
        fields = ['customer_uid', 'contact_name', 'contact_phone']

    def validate_customer_uid(self, value):
        customer = Customer.get_customer_by_uid(value)
        if not customer:
            raise serializers.ValidationError("客户不存在")
        return value

    def create(self, validated_data):
        customer_uid = validated_data.pop('customer_uid')
        customer = Customer.get_customer_by_uid(customer_uid)
        return CustomerContact.objects.create(customer=customer, **validated_data)


# 客户联系人更新序列化器
class CustomerContactUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomerContact
        fields = ['contact_name', 'contact_phone']


# 客户联系人详情序列化器
class CustomerContactDetailSerializer(serializers.ModelSerializer):
    # 客户信息
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    customer_uid = serializers.CharField(source='customer.uid', read_only=True)
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()

    class Meta:
        model = CustomerContact
        fields = ['contact_id', 'contact_name', 'contact_phone', 'customer_name', 'customer_uid', 'created_at', 'updated_at']