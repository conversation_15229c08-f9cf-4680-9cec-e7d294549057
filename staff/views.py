from rest_framework.views import APIView

from core.resp import make_error_response, make_success_response
from core.view import PaginationListBaseView
from staff.enums import Department, Gender, Position, Region
from staff.models import Staff
from staff.serializers import StaffCreateAndUpdateSerializer, StaffDetailSerializer, StaffListSerializer


# 员工列表
class StaffListView(PaginationListBaseView):
    authentication_classes = []
    permission_classes = []
    staff_required_permission = None
    
    serializer_class = StaffListSerializer
    response_msg = "Success"
    error_response_msg = "Failed"
    search_fields = ['name', 'phone', 'email', 'department', 'position', 'region']
    additional_data = None
    audit_log_message = ""
    
    def get_queryset(self):
        base_queryset = Staff.objects.filter().order_by('-join_date')
        
        department = self.request.query_params.get('department', None)
        position = self.request.query_params.get('position', None)
        region = self.request.query_params.get('region', None)
        gender = self.request.query_params.get('gender', None)
        status = self.request.query_params.get('status', True)
        
        if department and department in Department.values:
            base_queryset = base_queryset.filter(department=department)
            
        if position and position in Position.values:
            base_queryset = base_queryset.filter(position=position)
            
        if region and region in Region.values:
            base_queryset = base_queryset.filter(region=region)
        
        if gender and gender in Gender.values:
            base_queryset = base_queryset.filter(gender=gender)
        
        return base_queryset

# 员工详情
class StaffDetailView(APIView):
    authentication_classes = []
    permission_classes = []

    def get(self, request, uid):
        staff = Staff.get_staff_by_uid(uid)
        if not staff:
            return make_error_response(msg="员工不存在")
        return make_success_response(StaffDetailSerializer(staff).data)

# 员工创建
class StaffCreateView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        
        data = request.data.copy()
        
        serializer = StaffCreateAndUpdateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_success_response(StaffDetailSerializer(serializer.instance).data)
        return make_error_response(serializer.errors)
    

# 员工更新
class StaffUpdateView(APIView):
    authentication_classes = []
    permission_classes = []

    def put(self, request, uid):
        staff = Staff.get_staff_by_uid(uid)
        if not staff:
            return make_error_response(msg="员工不存在")
        
        data = request.data.copy()
        
        serializer = StaffCreateAndUpdateSerializer(staff, data=data)
        if serializer.is_valid():
            serializer.save()
            return make_success_response(StaffDetailSerializer(serializer.instance).data)
        return make_error_response(serializer.errors)
    
    
    
# 员工离职
class StaffLeaveView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request, uid):
        
        staff = Staff.get_staff_by_uid(uid)
        
        if not staff:
            return make_error_response(msg="员工不存在")
        
        staff.delete()
        
        return make_success_response(msg="员工离职成功")