from django.db import models


# 性别枚举
class Gender(models.TextChoices):
    # 男
    MALE = 'MALE', '男'
    # 女
    FEMALE = 'FEMALE', '女'

# 员工部门枚举
class Department(models.TextChoices):
    # 设计团队
    DESIGN_TEAM = 'DESIGN_TEAM', '设计团队'
    # 工程团队
    ENGINEERING_TEAM = 'ENGINEERING_TEAM', '工程团队'
    # 售后团队
    AFTER_SALES_TEAM = 'AFTER_SALES_TEAM', '售后团队'
    # 销售团队
    SALES_TEAM = 'SALES_TEAM', '销售团队'
    # 运营
    OPERATION_TEAM = 'OPERATION_TEAM', '运营团队'
    
# 员工职位枚举
class Position(models.TextChoices):
    # 设计主管
    DESIGN_LEADER = 'DESIGN_LEADER', '设计主管'
    # 设计师
    DESIGNER = 'DESIGNER', '设计师'
    # 工程主管
    ENGINEERING_LEADER = 'ENGINEERING_LEADER', '工程主管'
    # 技术员
    ENGINEER = 'ENGINEER', '技术员'
    # 售后主管
    AFTER_SALES_LEADER = 'AFTER_SALES_LEADER', '售后主管'
    # 售后员
    AFTER_SALES_ENGINEER = 'AFTER_SALES_ENGINEER', '售后技术员'
    # 销售主管
    SALES_LEADER = 'SALES_LEADER', '销售主管'
    # 销售支持
    Sales_SUPPORT = 'Sales_SUPPORT', '销售支持'
    # 销售
    SALES = 'SALES', '销售'
    # 产品经理
    PRODUCT_MANAGER = 'PRODUCT_MANAGER', '产品经理'
    # 运营专员
    OPERATION_SPECIALIST = 'OPERATION_SPECIALIST', '运营专员'
    

# 区域枚举
class Region(models.TextChoices):
    # 北部产品中心矩阵
    NORTH_PRODUCT_CENTER_MATRIX = 'NORTH_PRODUCT_CENTER_MATRIX', '北部产品中心矩阵'
    # 南部产品中心矩阵
    SOUTH_PRODUCT_CENTER_MATRIX = 'SOUTH_PRODUCT_CENTER_MATRIX', '南部产品中心矩阵'
    # 中资产品中心矩阵
    CHINA_PRODUCT_CENTER_MATRIX = 'CHINA_PRODUCT_CENTER_MATRIX', '中资产品中心矩阵'
    # 岘港产品中心矩阵
    DANANG_PRODUCT_CENTER_MATRIX = 'DANANG_PRODUCT_CENTER_MATRIX', '岘港产品中心矩阵'
    # 河内中心
    HANOI_CENTER_MATRIX = 'HANOI_CENTER_MATRIX', '河内中心矩阵'