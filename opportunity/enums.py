from django.db import models

# 项目类型枚举
class ProjectTypeEnum(models.TextChoices):
    # 新梯项目
    NEW_LIFT_PROJECT = 'NEW_LIFT_PROJECT','新梯项目'
    # 旧梯改造
    OLD_LIFT_RENOVATION = 'OLD_LIFT_RENOVATION', '旧梯改造'
    # 更新项目
    UPDATE_PROJECT = 'UPDATE_PROJECT', '更新项目'
    

# 商务运营关系枚举
class BusinessOperationRelationEnum(models.TextChoices):
    # 切入业主 (Contacting owner)
    CONTACTING_OWNER = 'CONTACTING_OWNER', '切入业主'
    # 切入总包 (Contacting maincon)
    CONTACTING_MAINCON = 'CONTACTING_MAINCON', '切入总包'
    # 切入业主及总包 (Contacting owner&maincon)
    CONTACTING_OWNER_AND_MAINCON = 'CONTACTING_OWNER_AND_MAINCON', '切入业主及总包'
    # 拉拢业主 (Convince owner)
    CONVINCE_OWNER = 'CONVINCE_OWNER', '拉拢业主'
    # 拉拢总包 (Convince maincon)
    CONVINCE_MAINCON = 'CONVINCE_MAINCON', '拉拢总包'
    # 拉拢业主及总包 (Convince owner& maincon)
    CONVINCE_OWNER_AND_MAINCON = 'CONVINCE_OWNER_AND_MAINCON', '拉拢业主及总包'
    # 未运营 (Null)
    NULL = 'NULL', '未运营'


# 竞品比对枚举
class CompetitorComparisonEnum(models.TextChoices):
    # 成本优势 (Price leading)
    PRICE_LEADING = 'PRICE_LEADING', '成本优势'
    # 成本劣势 (Price behind)
    PRICE_BEHIND = 'PRICE_BEHIND', '成本劣势'
    # 服务优势 (Service leading)
    SERVICE_LEADING = 'SERVICE_LEADING', '服务优势'
    # 服务劣势 (Service behind)
    SERVICE_BEHIND = 'SERVICE_BEHIND', '服务劣势'
    # 品牌优势 (Brand leading)
    BRAND_LEADING = 'BRAND_LEADING', '品牌优势'
    # 品牌劣势 (Brand behind)
    BRAND_BEHIND = 'BRAND_BEHIND', '品牌劣势'
    # 交期优势 (Duration leading)
    DURATION_LEADING = 'DURATION_LEADING', '交期优势'
    # 交期劣势 (Duration behind)
    DURATION_BEHIND = 'DURATION_BEHIND', '交期劣势'
    # 未知 (Null)
    NULL = 'NULL', '未知'
    
    

# 电梯类型
class ElevatorTypeEnum(models.TextChoices):
    # 乘客电梯
    PASSENGER_ELEVATOR = 'PASSENGER_ELEVATOR', '乘客电梯'
    # 货梯
    FREIGHT_ELEVATOR = 'FREIGHT_ELEVATOR', '货梯'
    # 医用电梯
    MEDICAL_ELEVATOR = 'MEDICAL_ELEVATOR', '医用电梯'
    # 观光电梯
    OBSERVATION_ELEVATOR = 'OBSERVATION_ELEVATOR', '观光电梯'
    # 汽车电梯
    CAR_ELEVATOR = 'CAR_ELEVATOR', '汽车电梯'
    # 家用电梯
    HOME_ELEVATOR = 'HOME_ELEVATOR', '家用电梯'
    # 消防员电梯
    FIREFIGHTER_ELEVATOR = 'FIREFIGHTER_ELEVATOR', '消防员电梯'