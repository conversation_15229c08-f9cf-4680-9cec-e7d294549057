from django.db import models

from core.enums import VietnamCityEnum, VietnamRegionEnum
from core.model import BaseModel, JSONListValidator
from customer.models import Customer, CustomerContact
from opportunity.enums import BusinessOperationRelationEnum, CompetitorComparisonEnum, ElevatorTypeEnum, ProjectTypeEnum
from staff.models import Staff


# 商机模型
class Opportunity(BaseModel):
    
    # - 基本信息
    # 项目编号
    number = models.CharField(max_length=255, verbose_name='项目编号', blank=True, default='')
    # 录入人
    input_user = models.ForeignKey(Staff, on_delete=models.SET_NULL, null=True, verbose_name='录入人')
    # 项目名称
    name = models.CharField(max_length=255, verbose_name='项目名称', blank=True, default='')
    # 项目类型
    type = models.CharField(max_length=255, verbose_name='项目类型', choices=ProjectTypeEnum.choices)
    # 预计签约日期
    expected_signing_date = models.DateField(verbose_name='预计签约日期')
    # 项目所在地
    location = models.CharField(max_length=255, verbose_name='项目所在地', choices=VietnamCityEnum.choices)
    # 项目地址
    address = models.CharField(max_length=255, verbose_name='项目地址', blank=True, default='')
    # 项目区域
    region = models.CharField(max_length=255, verbose_name='项目区域', choices=VietnamRegionEnum.choices)
    # 是否跨区
    is_cross_region = models.BooleanField(verbose_name='是否跨区')
    
    # - 商机成交几率
    # 商务关系运营
    business_relation = models.CharField(max_length=50,choices=BusinessOperationRelationEnum.choices,verbose_name='商务关系运营')
    # 竞争品牌
    competition_brand = models.CharField(max_length=255, verbose_name='竞争品牌', blank=True, default='')
    # 竞品比对（多选）
    competitor_comparison = models.JSONField(verbose_name='竞品比对', validators=[JSONListValidator(CompetitorComparisonEnum.choices)])
    
    # - 客户及对接信息
    # 客户
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name='客户')
    # 客户项目经理
    customer_pm = models.ForeignKey(CustomerContact, on_delete=models.CASCADE, verbose_name='客户项目经理')
    # 对接销售
    sales = models.ForeignKey(Staff, on_delete=models.SET_NULL, null=True, verbose_name='对接销售')
    
    # - 产品及交付信息
    # 总台数
    total_count = models.PositiveIntegerField(verbose_name='总台数')
    # 预算
    budget = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='预算')
    # 预计交货时间
    expected_delivery_time = models.DateTimeField(verbose_name='预计交货时间')
    # 电梯类型 (多选)
    elevator_type = models.JSONField(verbose_name='电梯类型', validators=[JSONListValidator(ElevatorTypeEnum.choices)])
    
    # - 项目详情
    # 项目描述
    description = models.TextField(verbose_name='项目描述', blank=True, default='')
    # 客户需求
    customer_demand = models.TextField(verbose_name='客户需求', blank=True, default='')
    # 备注
    remark = models.TextField(verbose_name='备注', blank=True, default='')
    
    # 项目图片
    images = models.JSONField(verbose_name='项目图片',blank=True,default=list)
    